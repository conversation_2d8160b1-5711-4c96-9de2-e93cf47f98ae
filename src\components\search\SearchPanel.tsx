import { useState, useEffect } from "react";
import { Search, X, Filter, MapPin } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";
import Categories from "@/components/category/Categories";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";

import type { Database } from "@/integrations/supabase/types";
import { useCategoryQuery } from "@/queries/useCategoryQuery";
import { NEARBY_DEFAULT_MAX_RADIUS_IN_KM, NEARBY_DEFAULT_MIN_RADIUS_IN_KM, NEARBY_DEFAULT_RADIUS_IN_KM } from "@/data/userSettings";
import type { FilterSettings } from "@/stores/useFilterStore";

type Category = Database["public"]["Tables"]["categories"]["Row"];

// Export the store type for backward compatibility
export type SearchFilters = FilterSettings;

interface SearchPanelProps {
  isOpen: boolean;
  onClose: () => void;
  filters: SearchFilters;
  onFiltersChange: (filters: SearchFilters) => void;
  onDistanceChange: (distance: number) => void;
  placeholder?: string;
  showLocationFilter?: boolean;
  showBusinessFilters?: boolean;
  showSortOptions?: boolean;
  showTextSearch?:boolean;

}

const SearchPanel = ({
  isOpen,
  onClose,
  filters,
  onFiltersChange,
  onDistanceChange,
  placeholder = "Cerca offerte e attività",
  showLocationFilter = false,
  showBusinessFilters = true,
  showSortOptions = false,
  showTextSearch = false,


}: SearchPanelProps) => {
  // const [categories, setCategories] = useState<Category[]>([]);
  // const [isLoadingCategories, setIsLoadingCategories] = useState(true);
  const { data: categoriesData, isLoading: isLoadingCategories } = useCategoryQuery();
  // useEffect(() => {
  //   const fetchCategories = async () => {
  //     try {
  //       const { data, error } = await supabase
  //         .from("categories")
  //         .select("*")
  //         .order("name");

  //       if (error) {
  //         toast.error("Errore nel caricamento delle categorie");
  //         return;
  //       }

  //       setCategories(data || []);
  //     } catch (error) {
  //       toast.error("Si è verificato un errore");
  //     } finally {
  //       setIsLoadingCategories(false);
  //     }
  //   };

  //   if (isOpen) {
  //     fetchCategories();
  //   }
  // }, [isOpen]);

  const handleSearchChange = (value: string) => {
    onFiltersChange({ ...filters, searchQuery: value });
  };

  const handleCategorySelect = (categoryId: string) => {
    onFiltersChange({ ...filters, selectedCategoryIds: 
      filters.selectedCategoryIds.includes(categoryId) 
        ? filters.selectedCategoryIds.filter(id => id !== categoryId)
        : [...filters.selectedCategoryIds, categoryId]
    });
  };

  const handleBusinessFilterChange = (key: 'withDeals' | 'withBookings') => {
    onFiltersChange({
      ...filters,
      [key]: !filters[key],
    });
  };

  const handleRadiusChange = (value: number[]) => {
    onFiltersChange({ ...filters, radius: value[0] });
    onDistanceChange(value[0]);
  };

  const handleSortChange = (sortBy: SearchFilters['sortBy']) => {
    onFiltersChange({ ...filters, sortBy });
  };

  const clearFilters = () => {
    onFiltersChange({
      searchQuery: '',
      selectedCategoryIds: [],
      withDeals: false,
      withBookings: false,
      radius: NEARBY_DEFAULT_RADIUS_IN_KM,
      sortBy: 'relevance',
      applicationAccess: filters.applicationAccess,
    });
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.searchQuery) count++;
    if (filters.selectedCategoryIds.length > 0) count++;
    if (filters.withDeals) count++;
    if (filters.withBookings) count++;
    if (filters.radius !== NEARBY_DEFAULT_RADIUS_IN_KM) count++;
    if (filters.sortBy !== 'relevance') count++;
    return count;
  };

  const activeFiltersCount = getActiveFiltersCount();

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onClose}
            className="fixed inset-0 bg-black/50 z-40"
          />

          {/* Search Panel */}
          <motion.div
            initial={{ y: "-100%" }}
            animate={{ y: 0 }}
            exit={{ y: "-100%" }}
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="fixed top-0 left-0 right-0 bg-white z-50 shadow-lg flex flex-col"
            style={{ height: '90vh' }}
          >
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex justify-between items-center p-4 border-b bg-white sticky top-0">
                <h2 className="text-xl font-semibold flex items-center gap-2">
                  <Filter className="h-5 w-5" />
                  Filtri e ricerca
                </h2>
                <div className="flex items-center gap-2">
                  {activeFiltersCount > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-sm"
                    >
                      Cancella ({activeFiltersCount})
                    </Button>
                  )}
                  <button
                    onClick={onClose}
                    className="p-2 hover:bg-gray-100 rounded-full transition-colors"
                  >
                    <X className="h-6 w-6" />
                  </button>
                </div>
              </div>

              {/* Search Bar */}
             {showTextSearch ? (
              <div className="p-4 border-b">
                <div className="relative">
                  <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
                  <input
                    type="text"
                    placeholder={placeholder}
                    className="w-full px-4 py-3 pl-12 bg-gray-50 rounded-xl border border-gray-200 focus:outline-none focus:ring-2 focus:ring-brand-primary focus:border-transparent"
                    value={filters.searchQuery}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    autoFocus
                  />
                </div>
              </div>): null}

              {/* Selected Categories - Compact Scrollable Line */}
              {filters.selectedCategoryIds.length > 0 && (
                <div className="px-4 py-3 border-b bg-gray-50/50">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-xs text-gray-500 font-medium">
                      {filters.selectedCategoryIds.length} categori{filters.selectedCategoryIds.length === 1 ? 'a' : 'e'} selezionat{filters.selectedCategoryIds.length === 1 ? 'a' : 'e'}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearFilters}
                      className="text-xs text-gray-400 hover:text-gray-600 h-6 px-2"
                    >
                      Cancella
                    </Button>
                  </div>
                  <div
                    className="flex gap-2 overflow-x-auto scrollbar-hide"
                    style={{
                      scrollbarWidth: 'none',
                      msOverflowStyle: 'none',
                      WebkitScrollbar: { display: 'none' }
                    }}
                  >
                    {filters.selectedCategoryIds.map((categoryId) => {
                      const category = categoriesData?.find(c => c.id === categoryId);
                      return category ? (
                        <motion.button
                          key={categoryId}
                          initial={{ opacity: 0, scale: 0.9 }}
                          animate={{ opacity: 1, scale: 1 }}
                          exit={{ opacity: 0, scale: 0.9 }}
                          onClick={() => handleCategorySelect(categoryId)}
                          className="flex-shrink-0 inline-flex items-center gap-1.5 px-2.5 py-1 bg-white text-brand-primary border border-brand-primary/30 rounded-lg text-xs font-medium hover:bg-brand-primary/5 hover:border-brand-primary/50 transition-all duration-200 shadow-sm"
                        >
                          <span className="truncate max-w-20">{category.name}</span>
                          <X className="h-3 w-3 opacity-60 hover:opacity-100" />
                        </motion.button>
                      ) : null;
                    })}
                  </div>
                </div>
              )}

              {/* Scrollable Content */}
              <div className="flex-1 overflow-y-auto">
                {/* Categories */}
                <div className="border-b">
                 
                  {isLoadingCategories ? (
                    <div className="px-4 pb-4">
                      <div className="flex space-x-4 overflow-x-auto">
                        {[1, 2, 3, 4].map((item) => (
                          <div key={item} className="flex-shrink-0">
                            <div className="w-16 h-16 bg-gray-200 rounded-2xl animate-pulse" />
                            <div className="w-12 h-4 bg-gray-200 rounded mt-2 animate-pulse" />
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="pb-4">
                      <Categories
                        categories={categoriesData}
                        selectedCategoryIds={filters.selectedCategoryIds}
                        onCategorySelect={handleCategorySelect}
                      />
                    </div>
                  )}
                </div>

                {/* Location Filter */}
                {showLocationFilter ? (
                  <div className="p-4 border-b">
                    <div className="flex items-center gap-2 mb-4">
                      <MapPin className="h-5 w-5 text-gray-600" />
                      <h3 className="text-lg font-medium text-gray-800">Distanza</h3>
                    </div>
                    <div className="space-y-4">
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>Raggio di ricerca</span>
                        <span>{filters.radius} km</span>
                      </div>
                      <Slider
                        value={[filters.radius]}
                        onValueChange={handleRadiusChange}
                        max={NEARBY_DEFAULT_MAX_RADIUS_IN_KM}
                        min={NEARBY_DEFAULT_MIN_RADIUS_IN_KM}
                        step={0.1}
                        className="w-full"
                      />
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{NEARBY_DEFAULT_MIN_RADIUS_IN_KM} km</span>
                        <span>{NEARBY_DEFAULT_MAX_RADIUS_IN_KM} km</span>
                      </div>
                    </div>
                  </div>
                ) : null}

                {/* Business Filters */}
                {showBusinessFilters ? (
                  <div className="p-4 border-b">
                    <div className="flex items-center gap-2 mb-4">
                      <Filter className="h-5 w-5 text-gray-600" />
                      <h3 className="text-lg font-medium text-gray-800">Filtri Attività</h3>
                    </div>
                    <div className="grid grid-cols-2 gap-3">
                      <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                        <input
                          type="checkbox"
                          checked={filters.withDeals}
                          onChange={() => handleBusinessFilterChange('withDeals')}
                          className="rounded border-gray-300 text-brand-primary focus:ring-brand-primary"
                        />
                        <span className="text-sm font-medium">Con offerte</span>
                      </label>
                      <label className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg cursor-pointer hover:bg-gray-100 transition-colors">
                        <input
                          type="checkbox"
                          checked={filters.withBookings}
                          onChange={() => handleBusinessFilterChange('withBookings')}
                          className="rounded border-gray-300 text-brand-primary focus:ring-brand-primary"
                        />
                        <span className="text-sm font-medium">Con prenotazioni</span>
                      </label>
                    </div>
                  </div>
                ): null
                }

                {/* Sort Options */}
                {showSortOptions ? (
                  <div className="p-4">
                    <h3 className="text-lg font-medium text-gray-800 mb-4">Ordina per</h3>
                    <div className="grid grid-cols-2 gap-3">
                      {[
                        { value: 'relevance', label: 'Rilevanza' },
                        { value: 'distance', label: 'Distanza' },
                        { value: 'rating', label: 'Valutazione' },
                        { value: 'price', label: 'Prezzo' },
                      ].map((option) => (
                        <button
                          key={option.value}
                          onClick={() => handleSortChange(option.value as SearchFilters['sortBy'])}
                          className={`p-3 rounded-lg text-sm font-medium transition-colors ${
                            filters.sortBy === option.value
                              ? 'bg-brand-primary text-white'
                              : 'bg-gray-50 text-gray-700 hover:bg-gray-100'
                          }`}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  </div>
                ):null}
              </div>
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

export default SearchPanel; 