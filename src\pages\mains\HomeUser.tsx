import { useState, useEffect } from "react";
import SwipeIntroOverlay from "@/components/ui/SwipeIntroOverlay";
import { useUserIntro } from "@/hooks/intro/useUserIntro";

import { useAuth } from "@/hooks/auth/useAuth";
import { useBusinesses } from "@/hooks/useBusinesses";
import { useBusinessMode } from "@/hooks/useBusinessMode";
import BusinessFilters, {
  FilterOptions,
} from "@/components/dashboard/BusinessFilters";
import SearchPanel from "@/components/search/ModernSearchPanel";
import { useSearchPanel } from "@/hooks/useSearchPanel";
import BottomNavigationBar from "@/components/toolbars/BottomNavigationBar";
import UnifiedHeader from "@/components/toolbars/UnifiedHeader";
import BusinessSection from "@/components/dashboard/BusinessSection";
import NextBooking from "@/components/dashboard/NextBooking";
import { useDashboardDeals } from "@/hooks/useDashboardDeals";


import { useNavigate } from "react-router-dom";
import SavingsAnalytics from "@/components/dashboard/SavingsAnalytics";
import PersonalizedRecommendations from "@/components/dashboard/PersonalizedRecommendations";
import DealCarousel from "@/components/deals/core/DealCarousel";
import DaySelector from "@/components/dashboard/DaySelector";
import { TimeSlider } from "@/components/dashboard/TimeSlider";
import AvailableDealsIndicator from "@/components/dashboard/AvailableDealsIndicator";
import UserLocationBox from "@/components/debug/UserLocationBox";
import CategoryDealsCarousel from "@/components/dashboard/deals/CategoryDealsCarousel";
import WeekendDealsCarousel from "@/components/dashboard/deals/WeekendDealsCarousel";
import FeaturedDealsCarousel from "@/components/dashboard/deals/FeaturedDealsCarousel";
import EndingSoonCarousel from "@/components/dashboard/deals/EndingSoonCarousel";
import { useUserPreferencesCheck } from "@/hooks/useUserPreferencesCheck";
import { UserPreferencesDialog } from "@/components/profile/UserPreferencesDialog";
import { useVoiceDialog } from "@/contexts/VoiceDialogContext";
import { Compass, Users, Camera, Cloud, Calendar } from "lucide-react";

const HomeUser = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user } = useAuth();
  const { isBusinessMode } = useBusinessMode();
  const { showIntro, hideIntro, resetIntro, closeIntro } = useUserIntro();
  const { openVoiceDialog } = useVoiceDialog();
  const [activeTab, setActiveTab] = useState("");
  const { hasPreferences, isLoading: isLoadingPreferences } = useUserPreferencesCheck();
  const [showPreferencesDialog, setShowPreferencesDialog] = useState(false);

  const tabs = [
    { id: "esperienze", label: "Esperienze", icon: Compass },
    { id: "social", label: "Social", badge: 3, icon: Users },
    { id: "ar", label: "AR Deals", icon: Camera },
    { id: "meteo", label: "Meteo", icon: Cloud },
    { id: "eventi", label: "Eventi", badge: 2, icon: Calendar }
  ];



  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Navigate based on tab
    switch (tabId) {
      case "esperienze":
        navigate("/experiences");
        break;
      case "social":
        navigate("/social");
        break;
      case "ar":
        navigate("/ar-deals");
        break;
      case "meteo":
        navigate("/meteo");
        break;
      case "eventi":
        navigate("/eventi");
        break;
      default:
        navigate("/");
    }
  };

  // Only use these hooks if user is authenticated
  const { userBusinesses, refreshBusinesses } = useBusinesses(
    user?.id || undefined,
    isAuthenticated && isBusinessMode
  );

  // Legacy business filters for business mode
  const [businessFilters, setBusinessFilters] = useState<FilterOptions>({
    withDeals: false,
    withBookings: false,
  });

  // Integrated SearchPanel for unified search across deals and businesses
  const {
    isOpen: isSearchPanelOpen,
    filters: searchFilters,
    openSearchPanel,
    closeSearchPanel,
    updateFilters: updateSearchFilters,
    hasActiveFilters,
    activeFiltersCount,
  } = useSearchPanel();

  const {
    selectedDate,
    timeValue,
    nearByRadius,
    setNearByRadius,
    handleDateSelect,
    handleTimeChange,
    resetToNow,
    getTimeFromPercentage,
    getSelectedDayName,

    // Deals data
    nearbyDeals,
    recentlyViewedDeals,
    favoriteDeals,
    featuredDeals,

    // Loading states
    isLoading,
    loadingDeals,
    loadingRecentlyViewed,
    loadingFavorites,
    loadingFeatured,

    // Stats
    businessCount,

    // Metriche di ricerca
    metrics,
  } = useDashboardDeals();

  const handleViewDealDetails = (dealId: string) => {
    navigate(`/deal/${dealId}`);
  };

  const handleOpenVoiceDialog = () => {
    openVoiceDialog();
  };

  const handleVoiceDialogClose = () => {
    // This is now handled by the global voice dialog context
    // No need to do anything here for the SwipeIntroOverlay
  };

  // Check if user needs to configure preferences
  useEffect(() => {
    if (isAuthenticated && user && !isLoadingPreferences && hasPreferences === false) {
      setShowPreferencesDialog(true);
    }
  }, [isAuthenticated, user, hasPreferences, isLoadingPreferences]);

  const handlePreferencesDialogConfirm = () => {
    setShowPreferencesDialog(false);
    navigate("/personalize-preferences");
  };

  // Filter businesses for business mode using both business filters and search filters
  const filteredBusinesses = userBusinesses.filter((business) => {
    // Apply legacy business filters
    if (!businessFilters.withDeals && !businessFilters.withBookings) {
      // No business filters applied
    } else {
      const hasDeals = business.deal_count && business.deal_count > 0;
      const hasBookings = business.booking_count && business.booking_count > 0;

      if (businessFilters.withDeals && businessFilters.withBookings) {
        if (!(hasDeals && hasBookings)) return false;
      } else if (businessFilters.withDeals) {
        if (!hasDeals) return false;
      } else if (businessFilters.withBookings) {
        if (!hasBookings) return false;
      }
    }

    // Apply search filters
    if (searchFilters.searchQuery) {
      const businessName = business.name || "";
      const matchesSearch = businessName
        .toLowerCase()
        .includes(searchFilters.searchQuery.toLowerCase());
      if (!matchesSearch) return false;
    }

    return true;
  });

  const selectedTimeString = getTimeFromPercentage(timeValue[0]);

  // Apply search filters to deals
  const filterDeals = (deals: any[]) => {
    if (
      !searchFilters.searchQuery &&
      (!searchFilters.selectedCategoryIds ||
        searchFilters.selectedCategoryIds.length === 0)
    ) {
      return deals;
    }

    return deals.filter((deal) => {
      if (!deal) return false;

      // Apply search query filter
      if (searchFilters.searchQuery) {
        const dealTitle = deal.title || "";
        const businessName = deal.business_name || deal.businesses?.name || "";
        const matchesSearch =
          dealTitle
            .toLowerCase()
            .includes(searchFilters.searchQuery.toLowerCase()) ||
          businessName
            .toLowerCase()
            .includes(searchFilters.searchQuery.toLowerCase());

        if (!matchesSearch) return false;
      }

      // Apply category filter
      if (
        searchFilters.selectedCategoryIds &&
        searchFilters.selectedCategoryIds.length > 0
      ) {
        // Handle different data structures for category ID
        const businessCategoryId =
          "business" in deal && deal.business
            ? deal.business.category_id
            : "businesses" in deal && deal.businesses
            ? deal.businesses.category_id
            : deal.category_id || deal.business_category_id;

        if (
          !businessCategoryId ||
          !searchFilters.selectedCategoryIds.includes(businessCategoryId)
        )
          return false;
      }

      return true;
    });
  };

  // Apply search filters to deal collections
  const filteredNearbyDeals = filterDeals(nearbyDeals);
  const filteredRecentlyViewedDeals = filterDeals(recentlyViewedDeals);
  const filteredFavoriteDeals = filterDeals(favoriteDeals);
  const filteredFeaturedDeals = filterDeals(featuredDeals || []);

  const showActiveFilterMessage = false; // searchFilters.searchQuery || searchFilters.selectedCategoryIds.length > 0;

  return (
    <div className="min-h-screen bg-gray-50">
      <UnifiedHeader
        variant="with-tabs"
        showGreeting={true}
        showDate={true}
        showSearch={true}
        onSearchClick={openSearchPanel}
        hasActiveFilters={hasActiveFilters}
        activeFiltersCount={activeFiltersCount}
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
      />

      {/* Search Panel */}
      <SearchPanel
        isOpen={isSearchPanelOpen}
        onClose={closeSearchPanel}
        filters={searchFilters}
        onFiltersChange={updateSearchFilters}
        placeholder="Cerca offerte e attività"
        showLocationFilter={true}
        showBusinessFilters={isBusinessMode}
        showTextSearch={false}
        showSortOptions={false}
        onDistanceChange={() => {}}
      />

      <main className="pt-36 pb-20 px-4">
        {isAuthenticated && isBusinessMode ? (
          <>
            <div className="mt-4 flex justify-end">
              <BusinessFilters
                filters={businessFilters}
                onFiltersChange={setBusinessFilters}
              />
            </div>
            <BusinessSection
              userBusinesses={filteredBusinesses}
              onCreateSuccess={refreshBusinesses}
            />
          </>
        ) : (
          <>
            {/* CatchUp Agent Button - only for authenticated users */}
            {/* {isAuthenticated && (
              <div className="mt-4">
                <CatchUpAgentButton />
              </div>
            )} */}
            <div className="my-4">
              <DaySelector
                selectedDate={selectedDate}
                onDateSelect={handleDateSelect}
                onResetToNow={resetToNow}
              />
              <TimeSlider
                value={timeValue}
                onValueChange={handleTimeChange}
                onResetToNow={resetToNow}
              />
            </div>
            {/* Only show next booking for authenticated users */}
            {isAuthenticated && <NextBooking />}

            {/* Debug component - only for authenticated users */}
            {isAuthenticated && <UserLocationBox />}

            {/* Search Results Indicator */}
            {showActiveFilterMessage ? (
              <div className="my-4 p-4 bg-brand-primary/10 rounded-lg border border-brand-primary/20">
                <div className="flex justify-between items-center">
                  <div>
                    <p className="text-sm font-medium text-brand-primary">
                      Filtri attivi
                      {searchFilters.searchQuery && (
                        <span className="ml-2 text-gray-600">
                          • "{searchFilters.searchQuery}"
                        </span>
                      )}
                      {searchFilters.selectedCategoryIds.length > 0 && (
                        <span className="ml-2 text-gray-600">
                          • {searchFilters.selectedCategoryIds.length} categorie
                          selezionate
                        </span>
                      )}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      Totale risultati:{" "}
                      {filteredNearbyDeals.length +
                        filteredFavoriteDeals.length +
                        filteredRecentlyViewedDeals.length +
                        filteredFeaturedDeals.length}
                    </p>
                  </div>
                  <button
                    onClick={() =>
                      updateSearchFilters({
                        ...searchFilters,
                        searchQuery: "",
                        selectedCategoryIds: [],
                      })
                    }
                    className="text-sm text-brand-primary hover:text-brand-primary/80 font-medium"
                  >
                    Cancella filtri
                  </button>
                </div>
              </div>
            ) : null}

            <div className="mt-4">
              <AvailableDealsIndicator
                count={businessCount}
                isLoading={isLoading}
                time={selectedTimeString}
                onRadiusChange={setNearByRadius}
                showRadius={true}
                radius={nearByRadius}
              />

              <DealCarousel
                title={
                  searchFilters.searchQuery
                    ? `Risultati per "${
                        searchFilters.searchQuery
                      }" ${getSelectedDayName()} alle ${selectedTimeString}`
                    : `Offerte vicino a te ${getSelectedDayName()} alle ${selectedTimeString}`
                }
                deals={filteredNearbyDeals}
                isLoading={isLoading || loadingDeals}
                onDealClick={handleViewDealDetails}
                variant="full"
                emptyMessage={
                  searchFilters.searchQuery ||
                  searchFilters.selectedCategoryIds.length > 0
                    ? "Nessuna offerta trovata con i filtri applicati"
                    : "Nessuna offerta disponibile al momento in questa zona"
                }
                showDetailedTimeSlots={false}
                hideTimeSlots={true}
                selectedTime={selectedTimeString}
                selectedDate={selectedDate}
                showVisitBusiness={true}
              />
            </div>
            {/* Category-based deals carousel */}
            <CategoryDealsCarousel className="mt-6" />
            
            {/* Weekend deals carousel */}
            <WeekendDealsCarousel className="mt-6" />
            
            {/* Featured deals carousel */}
            <FeaturedDealsCarousel className="mt-6" />
            
            {/* Ending soon deals carousel */}
            <EndingSoonCarousel className="mt-6" />
            {/* Only show personalized content for authenticated users */}
            {isAuthenticated && (
              <>
                {/* <SavingsAnalytics />*/}
                <PersonalizedRecommendations />

                <DealCarousel
                  title="Offerte Preferite"
                  deals={filteredFavoriteDeals}
                  isLoading={loadingFavorites}
                  onDealClick={handleViewDealDetails}
                  showFavoriteButton={false}
                  emptyMessage={
                    searchFilters.searchQuery ||
                    searchFilters.selectedCategoryIds.length > 0
                      ? "Nessuna offerta preferita trovata con i filtri applicati"
                      : "Non hai ancora offerte preferite"
                  }
                  variant="compact"
                  showDetailedTimeSlots={true}
                  showVisitBusiness={true}
                />

                <DealCarousel
                  title="Visti di Recente"
                  deals={filteredRecentlyViewedDeals}
                  isLoading={loadingRecentlyViewed}
                  onDealClick={handleViewDealDetails}
                  emptyMessage={
                    searchFilters.searchQuery ||
                    searchFilters.selectedCategoryIds.length > 0
                      ? "Nessuna offerta recente trovata con i filtri applicati"
                      : "Non hai ancora visualizzato offerte"
                  }
                  variant="compact"
                  showDetailedTimeSlots={true}
                  showVisitBusiness={true}
                />
              </>
            )}

            {/* Show featured deals to all users */}
            <DealCarousel
              title="Offerte in Evidenza"
              deals={filteredFeaturedDeals}
              isLoading={loadingFeatured}
              onDealClick={handleViewDealDetails}
              variant="full"
              emptyMessage={
                searchFilters.searchQuery ||
                searchFilters.selectedCategoryIds.length > 0
                  ? "Nessuna offerta in evidenza trovata con i filtri applicati"
                  : "Nessuna offerta in evidenza al momento"
              }
              showDetailedTimeSlots={true}
              showVisitBusiness={true}
            />
          </>
        )}
      </main>
      <BottomNavigationBar
        isBusiness={isBusinessMode}
        showVoiceButton={true}
        forceOpenVoiceDialog={false}
        onVoiceDialogClose={handleVoiceDialogClose} // required for the SwipeIntroOverlay  to work
      />

      {/* User Preferences Dialog */}
      <UserPreferencesDialog
        isOpen={showPreferencesDialog}
        onConfirm={handlePreferencesDialogConfirm}
      />

      {/* Swipe Introduction Overlay */}
      {showIntro && (
        <SwipeIntroOverlay
          onClose={closeIntro}
          onDontShowAgain={hideIntro}
          onOpenVoiceDialog={handleOpenVoiceDialog}
        />
      )}
    </div>
  );
};

export default HomeUser;
